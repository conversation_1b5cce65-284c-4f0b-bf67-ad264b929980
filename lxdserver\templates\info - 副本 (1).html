<style type="text/css" media="all">
.card-body {
    flex: 1 1 auto;
    min-height: 1px;
    padding: 1.25rem;
}
.border-left-primary {
    border-left: .25rem solid #4e73df!important;
}
.border-left-success {
    border-left: .25rem solid #1cc88a!important;
}
.border-left-info {
    border-left: .25rem solid #36b9cc!important;
}
.border-left-warning {
    border-left: .25rem solid #f6c23e!important;
}
.text-gray-300 {
    color: #dddfeb!important;
}
.text-gray-800 {
    color: #5a5c69!important;
}
.font-weight-bold {
    font-weight: 700!important;
}
.text-xs {
    font-size: .7rem;
}
.text-uppercase {
    text-transform: uppercase!important;
}
.h-100 {
    height: 100%!important;
}
.py-2, .py-3 {
    padding-top: .5rem!important;
    padding-bottom: .5rem!important;
}
.py-3 {
    padding-top: .75rem!important;
    padding-bottom: .75rem!important;
}
.shadow {
    box-shadow: 0 .15rem 1.75rem 0 rgba(58,59,69,.15)!important;
}
.progress-sm {
    height: .5rem;
}
.card-body ul {
    padding-left: 1.2rem;
    line-height: 1.8;
}
</style>

<div class="card shadow mb-4">
    <div class="card-body">
        <ul>
    <br/>
    - 1. 系统开通时已为您设置初始密码，可直接用于登录。登录用户名是<strong>root</strong>。
    <br/>
    - 2. SSH端口信息请前往<strong>NAT转发</strong>页面查看。
    <br/>  
    - 3. 部分操作（例如创建实例、重装系统）可能耗时较长，请您耐心等待，并停留在当前操作页面。
    <br/>
    - 4. 所有内存小于512MB的服务器都<strong>不建议</strong>运行任何测试脚本，若强行运行可能导致<strong>无法连接、死机等</strong>问题。若出现此等问题建议<strong>重启或者重装</strong>解决。
    <br/>
        </ul>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-xl-3 col-md-6 mb-2">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">处理器</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{$data.CPUCores} Core</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-microchip fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-2">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">内存</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{$data.TotalRam} MB</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-memory fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-2">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">硬盘</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{$data.TotalDisk} MB</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-hdd fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-2">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">流量</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{$data.Bandwidth} GB</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>