<style>
.NAT_btn {
  display: flex;
  align-items: center;
}
.select-protocol {
  position: relative;
}
#select-protocol {
  display: none;
}
.container {
  width: 100%;
}
.public_title {
  display: flex;
  justify-content: space-between;
}
.public_title_text {
  font-size: 16px;
  line-height: 28px;
  font-weight: bold;
}
.public_title_ul {
  list-style: none;
}
.public_title_ul > li {
  width: 4px;
  height: 4px;
  float: left;
  margin-right: 10px;
  background-color: #96cdcd;
  border-radius: 50%;
  transition: all 0.2s;
}
.btn_main {
  outline: none;
  width: 100px;
  height: 30px;
  border-style: none;
  border-radius: 4px;
  color: #fff;
  transition: all 0.2s;
}
.purple {
  background-color: #96cdcd;
  border: 1px solid #96cdcd;
  border-radius: 4px;
}
.purple:hover {
  background-color: #99b7fd !important;
}
.table-note {
  font-size: 14px;
  color: red;
  margin-left: 15px;
}
.card-body {
    padding: 1.25rem;
}
.shadow {
    box-shadow: 0 .15rem 1.75rem 0 rgba(58,59,69,.15)!important;
}
.font-weight-bold {
    font-weight: 700!important;
}
.py-3 {
    padding-top: .75rem!important;
    padding-bottom: .75rem!important;
}
.horizontal-form-container {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: 1rem;
}
.horizontal-form-container .form-group-item {
    flex: 1 1 180px;
    min-width: 150px;
}
.horizontal-form-container .form-buttons {
    align-self: center;
}
.horizontal-form-container .selectItem .filter-text, .horizontal-form-container .selectItem .filter-list {
    width: 100%;
}
.invalid-feedback {
    display: none; 
    width: 100%;
    margin-top: .25rem;
    font-size: .875em;
    color: #dc3545;
}
.is-invalid ~ .invalid-feedback {
    display: block;
}

.table-pagination-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 1.25rem;
    flex-wrap: wrap;
    gap: 1rem;
}
#rows-per-page {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: .2rem;
    border: 1px solid #d1d3e2;
    height: auto;
    width: auto;
    display: inline-block;
}
.pagination {
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: .25rem;
    margin: 0;
}
.page-item .page-link {
    position: relative;
    display: block;
    padding: 0.25rem 0.65rem;
    font-size: 0.875rem;
    margin-left: -1px;
    line-height: 1.5;
    color: #96cdcd;
    background-color: #fff;
    border: 1px solid #dee2e6;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.page-item.active .page-link {
    z-index: 1;
    color: #fff;
    background-color: #96cdcd;
    border-color: #96cdcd;
}
.page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    cursor: auto;
    background-color: #fff;
    border-color: #dee2e6;
}
.page-item:first-child .page-link {
    margin-left: 0;
    border-top-left-radius: .2rem;
    border-bottom-left-radius: .2rem;
}
.page-item:last-child .page-link {
    border-top-right-radius: .2rem;
    border-bottom-right-radius: .2rem;
}

/* 美化弹窗样式 */
.swal2-popup {
    border-radius: 10px !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
}
.swal2-title {
    font-size: 1.5rem !important;
    font-weight: 600 !important;
}
.swal2-content {
    font-size: 1rem !important;
}
.swal2-styled {
    font-weight: 500 !important;
    padding: 10px 24px !important;
}
.swal2-styled.swal2-confirm {
    background-color: #96cdcd !important;
    box-shadow: 0 4px 8px rgba(150, 205, 205, 0.3) !important;
}
.swal2-styled.swal2-cancel {
    background-color: #f0f0f0 !important;
    color: #333 !important;
}
</style>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">NAT转发规则</h6>
    </div>
    <div class="card-body">

        <div class="NAT_btn">
          <button type="button" id="toggle-create-form-btn" class="btn btn-primary waves-effect waves-light">创建</button>
          <div class="table-note">
            {if $msg}
                提醒：{$msg}
            {/if}
          </div>
        </div>
        
        <div id="create-form-wrapper" style="display: none; padding: 1rem 0;">
            <form id="create-nat-form">
                <div class="horizontal-form-container">
                    <div class="form-group-item">
                        <input required name="dport" type="number" class="form-control" min="10000" max="65535" id="dportinput" placeholder="外网端口 (10000-65535)">
                        <div class="invalid-feedback" id="externalPort-feedbacklxdserveracl"></div>
                    </div>
                    <div class="form-group-item">
                        <input required name="sport" type="number" class="form-control" min="1" max="65535" id="sportinput" placeholder="内网端口 (1-65535)">
                        <div class="invalid-feedback" id="internalPort-feedbacklxdserveracl"></div>
                    </div>
                    <div class="form-group-item selectItem">
                        <div class="protocol nokvmprotocollxdserveracl">
                            <div class="filter-text">
                                <input class="filter-title" type="text" readonly placeholder="TCP" />
                                <i class="icon icon-filter-arrow"></i>
                            </div>
                            <select name="dtype">
                                <option value="tcp" selected>TCP</option>
                                <option value="udp">UDP</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-buttons">
                        <button type="button" class="btn btn-primary waves-effect waves-light confirm-btnlxdserveracl">确定</button>
                        <button type="button" class="btn btn-secondary waves-effect waves-light" style="margin-left:5px" onclick="$('#toggle-create-form-btn').click(); resetCreateForm();">取消</button>
                    </div>
                </div>
            </form>
        </div>

        <div class="table-responsive">
            <table class="table mb-0 mt-3">
              <thead>
                <tr>
                  <th>外网端口</th>
                  <th>内网端口</th>
                  <th>协议</th>
                  <th style="width:90px">管理</th>
                </tr>
              </thead>
    
              <tbody id="nat-rules-tbody">
                {if $list}
                    {foreach $list as $list_item }
                    <tr>
                    <td>{$list_item.Dport}</td>
                    <td>{$list_item.Sport}</td>
                    <td>{$list_item.Dtype}</td>
                    <td>
                        <button type="button" class="btn btn-link deleteNATlxdserveracl" data-dtype="{$list_item.Dtype}" data-dport="{$list_item.Dport}" data-sport="{$list_item.Sport}">删除</button>
                    </td>
                    </tr>
                    {/foreach}
                {else}
                    <tr>
                        <td colspan="4" style="text-align:center;">暂无NAT转发规则。</td>
                    </tr>
                {/if}
              </tbody>
            </table>
        </div>

        <div class="table-pagination-container" style="display: none;">
            <span>显示条数:</span>
            <select id="rows-per-page" class="form-control" style="border-radius: 4px;">
                <option value="5" selected>5</option>
                <option value="10">10</option>
                <option value="20">20</option>
            </select>
            <nav>
                <ul class="pagination" id="pagination-controls"></ul>
            </nav>
        </div>

        <div style="display: none" id="loading-circlelxdserveracl">
          <div class="loading_limit">
            <div class="loading_inner">
              <div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div>
            </div>
          </div>
        </div>
    </div>
</div>

<script>
; jQuery.fn.selectFilter = function (options) {
	var defaults = {
		callBack: function (res) { }
	};
	var ops = $.extend({}, defaults, options);
	var selectList = $(this).find('select option');
	var that = this;
	var html = '';

	html += '<ul class="filter-list">';

	$(selectList).each(function (idx, item) {
		var val = $(item).val();
		var valText = $(item).html();
		var selected = $(item).attr('selected');
		var disabled = $(item).attr('disabled');
		var isSelected = selected ? 'filter-selected' : '';
		var isDisabled = disabled ? 'filter-disabled' : '';
		if (selected) {
			html += '<li class="' + isSelected + '" data-value="' + val + '"><a title="' + valText + '">' + valText + '</a></li>';
			$(that).find('.filter-title').val(valText);
		} else if (disabled) {
			html += '<li class="' + isDisabled + '" data-value="' + val + '"><a>' + valText + '</a></li>';
		} else {
			html += '<li data-value="' + val + '"><a title="' + valText + '">' + valText + '</a></li>';
		};
	});

	html += '</ul>';
	$(that).append(html);
	$(that).find('select').hide();

	$(that).on('click', '.filter-text', function () {
		$(that).find('.filter-list').slideToggle(100);
		$(that).find('.filter-list').toggleClass('filter-open');
		$(that).find('.icon-filter-arrow').toggleClass('filter-show');
	});

	$(that).find('.filter-list li').not('.filter-disabled').on('click', function () {
		var val = $(this).data('value');
		var valText = $(this).find('a').html();
		$(that).find('.filter-title').val(valText);
		$(that).find('.icon-filter-arrow').toggleClass('filter-show');
		$(this).addClass('filter-selected').siblings().removeClass('filter-selected');
		$(this).parent().slideToggle(50);
		for (var i = 0; i < selectList.length; i++) {
			var selectVal = selectList.eq(i).val();
			if (val == selectVal) {
				$(that).find('select').val(val);
			};
		};
		ops.callBack(val);
	});

	$(document).on('mousedown', function (e) {
		closeSelect(that, e);
	});
	$(document).on('touchstart', function (e) {
		closeSelect(that, e);
	});

	function closeSelect (that, e) {
		var filter = $(that).find('.filter-list'),
			filterEl = $(that).find('.filter-list')[0];
		var filterBoxEl = $(that)[0];
		var target = e.target;
		if (filterEl !== target && !$.contains(filterEl, target) && !$.contains(filterBoxEl, target)) {
			filter.slideUp(50);
			$(that).find('.filter-list').removeClass('filter-open');
			$(that).find('.icon-filter-arrow').removeClass('filter-show');
		};
	}
};
</script>
<script>
  $(document).ready(function(){
      $('#toggle-create-form-btn').on('click', function() {
          $('#create-form-wrapper').slideToggle();
      });

      $('.nokvmprotocollxdserveracl').selectFilter({
        callBack: function (val) {
        }
      });

      function handleActionSuccess(message) {
        Swal.fire({
            icon: 'success',
            title: '操作成功',
            text: message || '操作已完成，请刷新网页查看最新数据。',
            showConfirmButton: true,
            confirmButtonText: '确定',
            confirmButtonColor: '#96cdcd'
        });
      }

      $('.deleteNATlxdserveracl').on('click', function () {
        var button = $(this);
        if (button.prop('disabled')) return;

        var dportval = button.data('dport');
        var sportval = button.data('sport');
        var dtypeval = button.data('dtype').toLowerCase();

        Swal.fire({
          title: '确定删除此转发吗？',
          text: '外网端口: ' + dportval + ', 内网端口: ' + sportval + ', 协议: ' + dtypeval.toUpperCase(),
          icon: 'question',
          showCancelButton: true,
          confirmButtonColor: '#96cdcd',
          cancelButtonColor: '#6c757d',
          confirmButtonText: '确认删除',
          cancelButtonText: '取消'
        }).then((result) => {
          if (result.value) {
            button.prop('disabled', true).html('删除中...');
            ajax({
              type: "post",
              url: "{$MODULE_CUSTOM_API}",
              data: { "func": "natdel", "dtype": dtypeval, "dport": dportval, "sport": sportval },
              success: function (data) {
                if (data.status == 'success' || (data.msg && (data.msg.includes('任务已提交') || data.msg.includes('删除') || data.msg.includes('成功')))) {
                  handleActionSuccess('删除操作已完成，请刷新网页查看最新数据。');
                } else {
                  Swal.fire({ 
                    icon: 'error', 
                    title: '删除失败', 
                    text: data.msg || "操作失败",
                    confirmButtonColor: '#96cdcd'
                  });
                  button.prop('disabled', false).html('删除');
                }
              },
              error: function (xhr, err) {
                Swal.fire({ 
                  icon: 'error', 
                  title: '请求失败', 
                  text: '无法连接到服务器，请稍后再试。',
                  confirmButtonColor: '#96cdcd'
                });
                button.prop('disabled', false).html('删除');
              }
            })
          }
        })
      });

      function updateTable(dport, sport, dtype) {
        // 直接添加新行到表格，无需刷新
        var newRow = '<tr>' +
                    '<td>' + dport + '</td>' +
                    '<td>' + sport + '</td>' +
                    '<td>' + dtype.toUpperCase() + '</td>' +
                    '<td>' +
                    '<button type="button" class="btn btn-link deleteNATlxdserveracl" data-dtype="' + dtype + '" data-dport="' + dport + '" data-sport="' + sport + '">删除</button>' +
                    '</td></tr>';
        
        if ($('#nat-rules-tbody tr td[colspan="4"]').length > 0) {
          // 如果当前表格显示的是"暂无NAT转发规则"，则替换整个内容
          $('#nat-rules-tbody').html(newRow);
        } else {
          // 否则追加新行
          $('#nat-rules-tbody').append(newRow);
        }
        
        // 重新绑定删除按钮事件
        $('.deleteNATlxdserveracl').off('click').on('click', function () {
          var button = $(this);
          if (button.prop('disabled')) return;

          var dportval = button.data('dport');
          var sportval = button.data('sport');
          var dtypeval = button.data('dtype').toLowerCase();

          Swal.fire({
            title: '确定删除此转发吗？',
            text: '外网端口: ' + dportval + ', 内网端口: ' + sportval + ', 协议: ' + dtypeval.toUpperCase(),
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#6e9aff',
            cancelButtonColor: '#d33',
            confirmButtonText: '确认删除',
            cancelButtonText: '取消'
          }).then((result) => {
            if (result.value) {
              button.prop('disabled', true).html('删除中...');
              ajax({
                type: "post",
                url: "{$MODULE_CUSTOM_API}",
                data: { "func": "natdel", "dtype": dtypeval, "dport": dportval, "sport": sportval },
                success: function (data) {
                  if (data.msg && data.msg.includes('任务已提交')) {
                    handleActionSuccess();
                  } else {
                    Swal.fire({ icon: 'error', title: '删除失败', text: data.msg || "操作失败" });
                    button.prop('disabled', false).html('删除');
                  }
                },
                error: function (xhr, err) {
                  Swal.fire({ icon: 'error', title: '请求失败', text: '无法连接到服务器' });
                  button.prop('disabled', false).html('删除');
                }
              })
            }
          });
        });
        
        // 更新分页
        var $tableBody = $('#nat-rules-tbody');
        var $allRows = $tableBody.find('tr').filter(function() { return $(this).find('td').length > 1; });
        var totalRows = $allRows.length;
        if (totalRows > 0) {
          setupPagination();
        }
      }

      $('.confirm-btnlxdserveracl').on('click', function () {
        const externalPort = document.getElementById("dportinput");
        const internalPort = document.getElementById("sportinput");
        const externalPortFeedback = document.getElementById("externalPort-feedbacklxdserveracl");
        const internalPortFeedback = document.getElementById("internalPort-feedbacklxdserveracl");
        let isValid = true;
        externalPort.classList.remove("is-invalid");
        internalPort.classList.remove("is-invalid");

        if (externalPort.value === "" || parseInt(externalPort.value) < 10000 || parseInt(externalPort.value) > 65535) {
          externalPortFeedback.innerHTML = "请填写有效的外网端口 (10000-65535)";
          externalPort.classList.add("is-invalid");
          isValid = false;
        }

        if (internalPort.value === "" || parseInt(internalPort.value) < 1 || parseInt(internalPort.value) > 65535) {
          internalPortFeedback.innerHTML = "请填写有效的内网端口 (1-65535)";
          internalPort.classList.add("is-invalid");
          isValid = false;
        }
        if (!isValid) return;

        var button = $(this);
        if (button.prop('disabled')) return;
        button.prop('disabled', true).html('提交中...');

        ajax({
          type: "post",
          url: "{$MODULE_CUSTOM_API}",
          data: $("#create-nat-form").serialize() + "&func=natadd",
          success: function (data) {
            if (data.status == 'success' || (data.msg && (data.msg.includes('任务已提交') || data.msg.includes('添加成功')))) {
                $('#create-form-wrapper').slideUp();
                resetCreateForm();
                
                Swal.fire({
                    icon: 'success',
                    title: '创建成功',
                    text: '新的NAT转发规则已添加，请刷新网页查看。',
                    confirmButtonText: '确定',
                    confirmButtonColor: '#96cdcd'
                });
                
                button.prop('disabled', false).html('确定');
            } else {
                Swal.fire({ 
                  icon: 'error', 
                  title: '创建失败', 
                  text: data.msg || "操作失败",
                  confirmButtonText: '确定',
                  confirmButtonColor: '#96cdcd'
                });
                button.prop('disabled', false).html('确定');
            }
          },
          error: function (xhr, err) {
            Swal.fire({ 
              icon: 'error', 
              title: '请求失败', 
              text: '无法连接到服务器，请稍后再试。',
              confirmButtonText: '确定',
              confirmButtonColor: '#96cdcd'
            });
            button.prop('disabled', false).html('确定');
          }
        })
    });

    var currentPage = 1;
    var $tableBody = $('#nat-rules-tbody');
    var $allRows = $tableBody.find('tr').filter(function() { return $(this).find('td').length > 1; });
    var totalRows = $allRows.length;

    function setupPagination() {
        var rowsPerPage = parseInt($('#rows-per-page').val());
        if (totalRows <= rowsPerPage) {
            $('.table-pagination-container').hide();
            $allRows.show(); 
            return;
        } else {
            $('.table-pagination-container').show();
        }

        var totalPages = Math.ceil(totalRows / rowsPerPage);
        var $paginationControls = $('#pagination-controls');
        $paginationControls.empty();

        $paginationControls.append('<li class="page-item" id="prev-page"><a class="page-link">&laquo;</a></li>');
        for (var i = 1; i <= totalPages; i++) {
            $paginationControls.append('<li class="page-item page-num" data-page="' + i + '"><a class="page-link">' + i + '</a></li>');
        }
        $paginationControls.append('<li class="page-item" id="next-page"><a class="page-link">&raquo;</a></li>');

        showPage(1);

        $paginationControls.off('click');

        $paginationControls.on('click', '#prev-page', function(e) {
            if (!$(this).hasClass('disabled')) showPage(currentPage - 1);
        });

        $paginationControls.on('click', '#next-page', function(e) {
            if (!$(this).hasClass('disabled')) showPage(currentPage + 1);
        });

        $paginationControls.on('click', '.page-num', function(e) {
            showPage(parseInt($(this).data('page')));
        });
    }

    function showPage(page) {
        currentPage = page;
        var rowsPerPage = parseInt($('#rows-per-page').val());
        var totalPages = Math.ceil(totalRows / rowsPerPage);

        var start = (page - 1) * rowsPerPage;
        var end = start + rowsPerPage;

        $allRows.hide().slice(start, end).show();

        $('.page-num').removeClass('active');
        $('.page-num[data-page="' + page + '"]').addClass('active');

        $('#prev-page').toggleClass('disabled', page === 1);
        $('#next-page').toggleClass('disabled', page === totalPages);
    }

    if (totalRows > 0) {
        setupPagination();
        $('#rows-per-page').on('change', setupPagination);
    }
  });

  function resetCreateForm() {
    $("#create-nat-form")[0].reset();
    document.getElementById("dportinput").classList.remove("is-valid", "is-invalid");
    document.getElementById("sportinput").classList.remove("is-valid", "is-invalid");
    var $selectProtocol = $('.nokvmprotocollxdserveracl');
    var defaultText = 'TCP';
    var defaultValue = 'tcp';
    $selectProtocol.find('.filter-title').val(defaultText);
    $selectProtocol.find('select').val(defaultValue);
    $selectProtocol.find('.filter-list li').removeClass('filter-selected');
    $selectProtocol.find('.filter-list li[data-value="' + defaultValue + '"]').addClass('filter-selected');
  }

  function ajax(options) {
    var xhr = new XMLHttpRequest() || new ActiveXObject("Microsoft,XMLHTTP");
    if (typeof (options.data) != 'string') {
      var str = "";
      for (var key in options.data) {
        str += "&" + key + "=" + options.data[key];
      }
      str = str.slice(1)
    } else {
      var str = options.data;
    }
    options.dataType = options.dataType || 'json';
    if (options.type == "get") {
      var url = options.url + "?" + str;
      xhr.open("get", url);
      xhr.setRequestHeader("Authorization", "JWT {$Think.get.jwt}");
      xhr.send();
    } else if (options.type == "post") {
      xhr.open("post", options.url);
      xhr.setRequestHeader("content-type", "application/x-www-form-urlencoded");
      xhr.setRequestHeader("Authorization", "JWT {$Think.get.jwt}");
      xhr.send(str)
    }
    xhr.onreadystatechange = function () {
      if (xhr.readyState == 4 && xhr.status == 200) {
        var d = xhr.responseText;
        try {
            d = JSON.parse(d);
        } catch (e) {
            options.error && options.error(xhr.status, 'JSON 解析错误');
            return;
        }
        options.success && options.success(d, xhr.responseXML)
      } else if (xhr.readyState == 4 && xhr.status != 200) {
        var errorResponseText = xhr.responseText;
        var parsedErrorData = errorResponseText;
        try {
            var tempParsed = JSON.parse(errorResponseText);
            if(typeof tempParsed === 'object' && tempParsed !== null) {
                parsedErrorData = tempParsed;
            }
        } catch (e) {}
        options.error && options.error(xhr.status, parsedErrorData);
      }
    }
  }
</script>