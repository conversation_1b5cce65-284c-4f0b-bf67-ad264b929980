body { }
#execOutput, #infoContent pre { white-space: pre-wrap; background-color: #f5f5f5; border: 1px solid #ccc; padding: 10px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.9em; }
#execOutput.success { border-color: #d4edda; background-color: #f8f9fa; color: #155724; }
#execOutput.error { border-color: #f5c6cb; background-color: #f8d7da; color: #721c24; }
#infoContent { background-color: #fff; border: none; padding: 0; }
.btn-processing {
    pointer-events: none;
    opacity: 0.7;
}
.btn-processing .spinner-border {
     margin-right: 5px;
}
#toastContainer {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1060;
}
.modal { z-index: 1050; }
.modal-backdrop { z-index: 1049; }
#sshModal { z-index: 1055; }
#sshModal .modal-backdrop { z-index: 1054; }

@media (min-width: 768px) {
     .container-list-mobile {
        display: none;
     }
     .container-list-desktop {
         display: block;
     }
}
 .container-list-mobile {
    display: block;
 }
 .container-list-mobile .card-actions {
    margin-top: 1rem;
 }
  .container-list-mobile .card-text small {
    font-weight: bold;
    margin-right: 5px;
  }
 .container-list-mobile .card-title {
     word-break: break-word;
 }
#natRulesList {
    margin-top: 15px;
    border-top: 1px solid #eee;
    padding-top: 15px;
}
#natRulesList h6 {
    margin-bottom: 10px;
}
#natRulesList ul {
    list-style: none;
    padding: 0;
}
#natRulesList li {
    background-color: #f9f9f9;
    border: 1px solid #eee;
    margin-bottom: 8px;
    padding: 10px;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}
#natRulesList li .rule-details {
    flex-grow: 1;
    margin-right: 10px;
    word-break: break-word;
}
 #natRulesList li .rule-actions {
    flex-shrink: 0;
    margin-left: auto;
 }
#loginContainer {
    max-width: 400px;
    width: 100%;
    padding: 20px;
}
.main-content-wrapper {
    min-height: calc(100vh - 56px - 48px);
}
.create-container-card {
    background-color: transparent !important;
    border: 1px solid #dee2e6 !important;
}
.create-container-card .card-header {
    background-color: transparent !important;
    border-bottom: 1px solid #dee2e6 !important;
    color: inherit !important;
    font-weight: bold;
}
.truncate-text {
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    vertical-align: middle;
}
.actions-dropdown .dropdown-menu {
    min-width: auto;
}
.actions-dropdown .dropdown-item {
    padding: .25rem 1rem;
}

.custom-container-list-row {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}
.custom-container-list-row:last-child {
    border-bottom: none;
}

.custom-container-list-header {
    display: flex;
    align-items: center;
    font-weight: bold;
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    padding: 0.5rem 0;
}

.custom-container-list-header .sortable {
    cursor: pointer;
    user-select: none;
}
.custom-container-list-header .sortable:hover {
    background-color: #e9ecef;
}
.sort-icon::after {
    font-size: 0.8em;
    margin-left: 5px;
    color: #999;
    content: '↕';
    display: inline-block;
    width: 1em;
}
.sort-icon.asc::after {
    content: '↑';
    color: #333;
}
.sort-icon.desc::after {
    content: '↓';
    color: #333;
}

/* Smaller font for desktop list to fit all columns */
.custom-container-list-header,
.custom-container-list-row {
    font-size: 13px;
    white-space: nowrap;
}

.custom-col {
    padding: 0 0.75rem;
    box-sizing: border-box;
    word-break: break-word;
}

.custom-col-name {
    flex: 1;
    min-width: 90px;
}

.custom-col-status {
    flex: 2;
    min-width: 80px;
    text-align: center;
}
.custom-col-status .badge {
    display: inline-block;
}

.custom-col-ip {
    flex: 2;
    min-width: 120px;
}

.custom-col-image {
    flex: 3;
    min-width: 180px;
}

.custom-col-created {
    flex: 2;
    min-width: 120px;
}

.custom-col-actions {
    flex: 1;
    min-width: 80px;
    text-align: center;
}
#terminal {
    height: 500px;
    background-color: #000;
    padding: 5px;
}
.xterm .xterm-viewport {
    overflow-y: hidden !important;
}