# 魔方财务-LXD 对接插件 (zjmf-lxd-server)

这是一个为 [魔方财务](https://www.google.com/search?q=https://www.zjmf.com/) (ZJMF) 系统开发的 LXD 对接插件，旨在为主机商提供一套完整、自动化的 LXD 容器销售与管理解决方案。

项目通过一个独立的后端服务与魔方财务插件相结合的模式，实现了高效、安全、功能丰富的 LXD 容器管理体验。

**详细的安装和使用文档，请参考 [项目 Wiki](https://github.com/xkatld/zjmf-lxd-server/wiki)。**

-----

## 核心功能

  * **自动化供应**：通过魔方财务自动创建、终止、暂停和恢复 LXD 容器，支持重装系统和重置密码功能。
  * **资源配置**：在魔方财务产品设置中灵活配置容器资源，包括 CPU 核心数、内存、硬盘大小、网络带宽和流量限制。
  * **客户端功能**：客户可在魔方财务的用户中心查看容器的实时状态、管理 NAT 转发规则。
  * **Web 管理面板**：后端服务自带一个独立的 Web 管理界面，用于集中监控和管理所有创建的 LXD 容器，并提供快捷操作。
  * **安全隔离**：通过独立的 Python API 服务器 和 Token 认证机制，将 LXD 主机与魔方财务面板有效隔离，增强安全性。
  * **网络自动配置**：自动设置网络规则并确保系统重启后容器网络正常工作，无需手动配置。

-----

## 端口转发实现方式更新说明

本项目最近对端口转发功能进行了重要升级，将实现方式从iptables规则转换为LXD内置的proxy设备。

### 更新内容

1. **端口转发实现改进**
   - 使用LXD的proxy设备实现端口转发，不再依赖iptables规则
   - 无需配置服务器网卡名和公网IP，简化配置流程
   - 容器删除时自动清理端口转发规则，提高可靠性

2. **app.ini配置简化**
   - 虽然配置中仍保留MAIN_INTERFACE和NAT_LISTEN_IP字段，但新版本的端口转发功能不再依赖这些值
   - 兼容性更好，不受服务器网络配置变化影响

3. **如何测试**
   - 使用提供的test_port_forward.py脚本测试端口转发功能
   ```bash
   python3 server/test_port_forward.py
   ```

### 常见问题

1. **现有端口转发规则怎么办？**
   - 旧的端口转发规则需要手动重新添加，可以通过魔方财务客户端重新添加
   - 系统会自动清理旧的iptables规则并创建新的proxy设备

2. **如果出现问题**
   - 检查容器是否正常运行
   - 使用`lxc config show <容器名称>`命令查看proxy设备是否正确配置
   - 确认服务器防火墙允许相关端口访问

### 更新后优势

1. 更高的可靠性 - 不依赖主机网络配置
2. 更简单的管理 - 由LXD自动管理端口转发
3. 更好的兼容性 - 与nat.sh脚本保持一致的实现方式
4. 自动清理 - 容器删除时自动删除端口转发规则

## 网络配置说明

### 自动网络配置

本项目现已支持自动配置和维护容器网络环境，确保容器能在系统重启后正常联网。主要功能包括：

1. **系统重启网络持久化**：通过systemd服务和rc.local确保网络规则在系统启动时自动应用
2. **自动配置IP转发**：自动设置Linux内核IP转发功能
3. **自动配置NAT规则**：为LXD容器网络配置适当的NAT和转发规则
4. **自动检测网络配置**：自动识别主网卡、公网IP和桥接网络信息

### 启用网络配置功能

项目在安装时自动配置网络环境，但如需单独配置，可执行：

```bash
# 在server目录下执行
cd server
chmod +x setup_network_service.sh
sudo ./setup_network_service.sh
```

### 手动配置

如需手动配置网络规则：

```bash
# 启用IP转发
echo 1 > /proc/sys/net/ipv4/ip_forward
echo "net.ipv4.ip_forward=1" >> /etc/sysctl.conf
sysctl -p

# 设置NAT规则（请将ens3替换为您的外网网卡名称，***********/24替换为您的LXD桥接网络）
iptables -t nat -A POSTROUTING -s ***********/24 -o ens3 -j MASQUERADE

# 设置FORWARD规则
iptables -A FORWARD -i lxdbr0 -o ens3 -j ACCEPT
iptables -A FORWARD -i ens3 -o lxdbr0 -m state --state RELATED,ESTABLISHED -j ACCEPT

# 保存iptables规则
apt install -y iptables-persistent
netfilter-persistent save
```

-----

## 项目截图

![image](https://github.com/user-attachments/assets/39fe815e-b1e2-449b-a6a6-1a9206aa7497)

![image](https://github.com/user-attachments/assets/659ccc24-d213-47bc-8b7d-89c18a93165e)

![image](https://github.com/user-attachments/assets/10db6034-7d85-44a1-b021-e3e87ea9a2e8)

![image](https://github.com/user-attachments/assets/f8311d1d-bcdc-4eed-bfd9-90bb69afa2d3)

![image](https://github.com/user-attachments/assets/951ea9a4-ffe3-46dd-8231-589dd725bf2a)

![image](https://github.com/user-attachments/assets/01e53d28-54fe-40be-9bb7-833cc361eb58)
